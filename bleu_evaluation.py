#!/usr/bin/env python
# coding: utf-8

"""
BLEU Score Evaluation Script for English-to-Chinese Translation
使用jieba进行中文分词，然后计算BLEU分数
"""

import torch
import torch.nn as nn
import jieba
import evaluate
import time
import os
from tqdm import tqdm
from model.transformer import build_transformer
from tokenization import PrepareData

# 初始化参数
PAD = 0
UNK = 1

def load_model_and_data(model_path, device):
    """加载模型和数据"""
    print(f"Loading model from: {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']
    src_vocab_size = checkpoint['src_vocab_size']
    tgt_vocab_size = checkpoint['tgt_vocab_size']
    
    # 重建模型
    model = build_transformer(
        src_vocab_size, tgt_vocab_size, 
        config['seq_len'], config['seq_len'], 
        config['d_model'], config['n_layer'], 
        config['h_num'], config['dropout'], 
        config['d_ff']
    ).to(device)
    
    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 重建数据处理器
    print("Preparing test data...")
    # 使用测试数据
    test_file = config['train_file'].replace('train', 'test')
    if not os.path.exists(test_file):
        test_file = config['dev_file']  # 如果没有测试文件，使用验证文件
    
    data = PrepareData(config['train_file'], test_file, 1, UNK, PAD)  # batch_size=1 for evaluation
    
    return model, data, config, checkpoint

def casual_mask(size):
    """创建因果掩码"""
    mask = torch.triu(torch.ones(1, size, size), diagonal=1).type(torch.int)
    return mask == 0

def greedy_decode(model, source, source_mask, tokenizer_tgt, max_len, device):
    """贪心解码"""
    bos_id = tokenizer_tgt.get('BOS')
    eos_id = tokenizer_tgt.get('EOS')
    
    encoder_output = model.encode(source, source_mask)
    decoder_input = torch.empty(1, 1).fill_(bos_id).type_as(source).to(device)
    
    while True:
        if decoder_input.size(1) == max_len:
            break
            
        decoder_mask = casual_mask(decoder_input.size(1)).type_as(source_mask).to(device)
        out = model.decode(encoder_output, source_mask, decoder_input, decoder_mask)
        prob = model.project(out[:, -1])
        _, next_word = torch.max(prob, dim=1)
        decoder_input = torch.cat([decoder_input, torch.empty(1, 1).type_as(source).fill_(next_word.item()).to(device)], dim=1)
        
        if next_word == eos_id:
            break
            
    return decoder_input.squeeze(0)

def chinese_tokenize(text):
    """使用jieba对中文文本进行分词"""
    # 移除BOS和EOS标记
    text = text.replace('BOS', '').replace('EOS', '').strip()
    # 使用jieba分词
    words = list(jieba.cut(text, cut_all=False))
    # 过滤空字符串
    words = [w for w in words if w.strip()]
    return ' '.join(words)

def evaluate_bleu(model, data, config, device, num_samples=None):
    """评估BLEU分数"""
    model.eval()
    
    predictions = []
    references = []
    
    # 如果指定了样本数量，则限制评估样本
    eval_data = data.dev_data[:num_samples] if num_samples else data.dev_data
    
    print(f"Evaluating on {len(eval_data)} test samples...")
    
    with torch.no_grad():
        for i, batch in enumerate(tqdm(eval_data, desc="Generating translations")):
            if i % 100 == 0:
                print(f"Processing sample {i+1}/{len(eval_data)}")
                
            encoder_input = batch.src.to(device)
            encoder_mask = batch.src_mask.to(device)
            
            # 生成翻译
            model_out = greedy_decode(model, encoder_input, encoder_mask, 
                                    data.cn_word_dict, config['seq_len'], device)
            
            # 转换为文本
            source_text = " ".join([data.en_index_dict[w] for w in data.dev_en[i]])
            target_text = " ".join([data.cn_index_dict[w] for w in data.dev_cn[i]])
            
            # 生成的翻译文本
            model_out_text = []
            for j in range(1, model_out.size(0)):  # 跳过BOS
                sym = data.cn_index_dict[model_out[j].item()]
                if sym != 'EOS':
                    model_out_text.append(sym)
                else:
                    break
            
            pred_text = " ".join(model_out_text)
            
            # 对中文进行分词
            pred_segmented = chinese_tokenize(pred_text)
            ref_segmented = chinese_tokenize(target_text)
            
            predictions.append(pred_segmented)
            references.append([ref_segmented])  # BLEU需要reference是列表的列表
    
    # 计算BLEU分数
    print("Calculating BLEU score...")
    bleu = evaluate.load("bleu")
    
    # 过滤空预测
    valid_predictions = []
    valid_references = []
    for pred, ref in zip(predictions, references):
        if pred.strip():  # 只保留非空预测
            valid_predictions.append(pred)
            valid_references.append(ref)
    
    if not valid_predictions:
        print("Warning: No valid predictions found!")
        return 0.0, predictions[:5], [ref[0] for ref in references[:5]]
    
    try:
        results = bleu.compute(predictions=valid_predictions, references=valid_references)
        bleu_score = results['bleu']
    except Exception as e:
        print(f"Error computing BLEU: {e}")
        bleu_score = 0.0
    
    return bleu_score, predictions[:5], [ref[0] for ref in references[:5]]

def main():
    print("BLEU Evaluation Script for English-to-Chinese Translation")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 模型路径
    model_path = 'save/models/model.pt'
    
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        print("Please train the model first using translator_en2cn.py")
        return
    
    # 加载模型和数据
    model, data, config, checkpoint = load_model_and_data(model_path, device)
    
    print("Building model...")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 评估BLEU分数
    start_time = time.time()
    bleu_score, sample_predictions, sample_references = evaluate_bleu(
        model, data, config, device, num_samples=200  # 限制评估样本数量以加快速度
    )
    eval_time = time.time() - start_time
    
    print(f"\nBLEU Score: {bleu_score:.4f}")
    print(f"Evaluation time: {eval_time:.2f} seconds")
    
    # 显示一些翻译示例
    print("\nExample translations:")
    print("-" * 50)
    for i, (pred, ref) in enumerate(zip(sample_predictions, sample_references)):
        print(f"\nExample {i+1}:")
        print(f"Reference: {ref}")
        print(f"Prediction: {pred}")
        if i >= 2:  # 只显示前3个例子
            break
    
    # 保存结果
    results = {
        'bleu_score': bleu_score,
        'evaluation_time': eval_time,
        'num_samples': len(data.dev_data),
        'sample_predictions': sample_predictions,
        'sample_references': sample_references
    }
    
    results_path = 'save/models/bleu_results.pt'
    torch.save(results, results_path)
    print(f"\nResults saved to: {results_path}")

if __name__ == "__main__":
    main()
