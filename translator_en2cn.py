#!/usr/bin/env python
# coding: utf-8

# Data Preparation: English-to-Chinese Translator Data

from model.transformer import build_transformer
from tokenization import PrepareData, MaskBatch
import time
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import os

import warnings
warnings.filterwarnings('ignore') # Filtering warnings


# init parameters
PAD = 0  # padding word-id
UNK = 1  # unknown word-id


DEBUG = False   # Debug / Learning Purposes.
# DEBUG = False # Build the model, better with GPU CUDA enabled.

def get_config(debug=True):
    if debug:
        return{
            'lr': 1e-2,
            'batch_size': 64,
            'num_epochs': 10,  # 增加训练轮数以获得更好的结果
            'n_layer': 3,
            'h_num': 8,
            'd_model': 128, # Dimensions of the embeddings in the Transformer
            'd_ff': 256, # Dimensions of the feedforward layer in the Transformer
            'dropout': 0.1,
            'seq_len': 60, # max length
            'train_file': 'data/en-cn/train_mini.txt',
            'dev_file': 'data/en-cn/dev_mini.txt',
            'save_file': 'save/models/model.pt',
            'save_dir': 'save/models/',
            'plot_file': 'save/models/training_loss.png'
        }
    else:
        return{
            'lr': 1e-4,
            'batch_size': 64,
            'num_epochs': 30,  # 增加训练轮数
            'n_layer': 6,
            'h_num': 8,
            'd_model': 256, # Dimensions of the embeddings in the Transformer
            'd_ff': 1024, # Dimensions of the feedforward layer in the Transformer
            'dropout': 0.1,
            'seq_len': 60, # max length
            'train_file': 'data/en-cn/train.txt',
            'dev_file': 'data/en-cn/dev.txt',
            'save_file': 'save/models/model.pt',
            'save_dir': 'save/models/',
            'plot_file': 'save/models/training_loss.png'
        }


def get_model(config, vocab_src_len, vocab_tgt_len):
    # Loading model using the 'build_transformer' function.
    # We will use the lengths of the source language and target language vocabularies, the 'seq_len', and the dimensionality of the embeddings
    model = build_transformer(vocab_src_len, vocab_tgt_len, config['seq_len'], config['seq_len'], config['d_model'],
                              config['n_layer'], config['h_num'], config['dropout'], config['d_ff'])
    return model



# get config
config = get_config(DEBUG) # Retrieving config settings

# 创建保存目录
os.makedirs(config['save_dir'], exist_ok=True)

# Setting up device to run on GPU to train faster
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device {device}")

# Data Preprocessing
data = PrepareData(config['train_file'], config['dev_file'], config['batch_size'], UNK, PAD)
src_vocab_size = len(data.en_word_dict); print(f"src_vocab_size {src_vocab_size}")
tgt_vocab_size = len(data.cn_word_dict); print(f"tgt_vocab_size {tgt_vocab_size}")

# Model
model = get_model(config, src_vocab_size, tgt_vocab_size).to(device)

# 打印模型参数数量
total_params = sum(p.numel() for p in model.parameters())
print(f"Total model parameters: {total_params:,}")



# Initializing CrossEntropyLoss function for training
# We ignore padding tokens when computing loss, as they are not relevant for the learning process
# We also apply label_smoothing to prevent overfitting
loss_fn = nn.CrossEntropyLoss(ignore_index=PAD, label_smoothing=0.).to(device)

optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'], eps = 1e-9)



def casual_mask(size):
    # Creating a square matrix of dimensions 'size x size' filled with ones
    mask = torch.triu(torch.ones(1, size, size), diagonal = 1).type(torch.int)
    return mask == 0


# Define function to obtain the most probable next token
def greedy_decode(model, source, source_mask, tokenizer_tgt, max_len, device):
    # Retrieving the indices from the start and end of sequences of the target tokens
    bos_id = tokenizer_tgt.get('BOS')
    eos_id = tokenizer_tgt.get('EOS')

    # Computing the output of the encoder for the source sequence
    encoder_output = model.encode(source, source_mask)
    # Initializing the decoder input with the Start of Sentence token
    decoder_input = torch.empty(1,1).fill_(bos_id).type_as(source).to(device)

    # Looping until the 'max_len', maximum length, is reached
    while True:
        if decoder_input.size(1) == max_len:
            break

        # Building a mask for the decoder input
        decoder_mask = casual_mask(decoder_input.size(1)).type_as(source_mask).to(device)

        # Calculating the output of the decoder
        out = model.decode(encoder_output, source_mask, decoder_input, decoder_mask)

        # Applying the projection layer to get the probabilities for the next token
        prob = model.project(out[:, -1])

        # Selecting token with the highest probability
        _, next_word = torch.max(prob, dim=1)
        decoder_input = torch.cat([decoder_input, torch.empty(1,1). type_as(source).fill_(next_word.item()).to(device)], dim=1)

        # If the next token is an End of Sentence token, we finish the loop
        if next_word == eos_id:
            break

    return decoder_input.squeeze(0) # Sequence of tokens generated by the decoder



# Defining function to evaluate the model on the validation dataset
# num_examples = 2, two examples per run
def run_validation(model, data, tokenizer_tgt, max_len, device, print_msg, num_examples=4):
    model.eval() # Setting model to evaluation mode
    count = 0 # Initializing counter to keep track of how many examples have been processed

    console_width = 80 # Fixed witdh for printed messages

    # Creating evaluation loop
    with torch.no_grad(): # Ensuring that no gradients are computed during this process
        for i, batch in enumerate(data.dev_data):
            count += 1
            encoder_input = batch.src.to(device)
            encoder_mask = batch.src_mask.to(device)

            # Ensuring that the batch_size of the validation set is 1
            assert encoder_input.size(0) ==  1, 'Batch size must be 1 for validation.'

            # Applying the 'greedy_decode' function to get the model's output for the source text of the input batch
            model_out = greedy_decode(model, encoder_input, encoder_mask, tokenizer_tgt, max_len, device)

            # Retrieving source and target texts from the batch
            source_text = " ".join([data.en_index_dict[w] for w in data.dev_en[i]])
            target_text = " ".join([data.cn_index_dict[w] for w in data.dev_cn[i]])

            # save all in the translation list
            model_out_text = []
            # convert id to Chinese, skip 'BOS' 0.
            print(model_out)
            for j in range(1, model_out.size(0)):
                sym = data.cn_index_dict[model_out[j].item()]
                if sym != 'EOS':
                    model_out_text.append(sym)
                else:
                    break

            # Printing results
            print_msg('-'*console_width)
            print_msg(f'SOURCE: {source_text}')
            print_msg(f'TARGET: {target_text}')
            print_msg(f'PREDICTED: {model_out_text}')

            # After two examples, we break the loop
            if count == num_examples:
                break


# Training model
print(">>>>>>> start train")
train_start = time.time()

# 初始化损失记录列表
train_losses = []
epoch_losses = []

# Initializing epoch and global step variables
initial_epoch = 0
global_step = 0

# Iterating over each epoch from the 'initial_epoch' variable up to the number of epochs informed in the config
for epoch in range(initial_epoch, config['num_epochs']):
    # Initializing an iterator over the training dataloader
    # We also use tqdm to display a progress bar
    batch_iterator = tqdm(data.train_data, desc = f'Processing epoch {epoch:02d}')

    epoch_loss = 0.0
    num_batches = 0

    # For each batch...
    for batch in batch_iterator:
        model.train() # Train the model

        # Loading input data and masks onto the GPU
        encoder_input = batch.src.to(device)
        decoder_input = batch.tgt.to(device)
        encoder_mask = batch.src_mask.to(device)
        decoder_mask = batch.tgt_mask.to(device)

        # Running tensors through the Transformer
        encoder_output = model.encode(encoder_input, encoder_mask)
        decoder_output = model.decode(encoder_output, encoder_mask, decoder_input, decoder_mask)
        proj_output = model.project(decoder_output)

        # Loading the target labels onto the GPU
        label = batch.tgt_y.to(device)

        # Computing loss between model's output and true labels
        loss = loss_fn(proj_output.view(-1, tgt_vocab_size), label.view(-1))

        # 记录损失
        train_losses.append(loss.item())
        epoch_loss += loss.item()
        num_batches += 1

        # Updating progress bar
        batch_iterator.set_postfix({f"loss": f"{loss.item():6.3f}"})

        # Performing backpropagation
        loss.backward()

        # Updating parameters based on the gradients
        optimizer.step()

        # Clearing the gradients to prepare for the next batch
        optimizer.zero_grad()

        global_step += 1 # Updating global step count

    # 计算平均epoch损失
    avg_epoch_loss = epoch_loss / num_batches
    epoch_losses.append(avg_epoch_loss)

    # 保存中间模型检查点
    if (epoch + 1) % 5 == 0 or epoch == config['num_epochs'] - 1:
        checkpoint_path = os.path.join(config['save_dir'], f'model_epoch_{epoch+1}.pt')
        torch.save({
            'epoch': epoch + 1,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': avg_epoch_loss,
            'config': config,
            'src_vocab_size': src_vocab_size,
            'tgt_vocab_size': tgt_vocab_size,
            'en_word_dict': data.en_word_dict,
            'cn_word_dict': data.cn_word_dict,
            'en_index_dict': data.en_index_dict,
            'cn_index_dict': data.cn_index_dict
        }, checkpoint_path)
        print(f"Model checkpoint saved at epoch {epoch+1}: {checkpoint_path}")

    # to evaluate model performance
    if epoch % 5 == 0:
        run_validation(model, data, data.cn_word_dict, config['seq_len'], device, lambda msg: batch_iterator.write(msg))

# 保存最终模型
torch.save({
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'config': config,
    'src_vocab_size': src_vocab_size,
    'tgt_vocab_size': tgt_vocab_size,
    'en_word_dict': data.en_word_dict,
    'cn_word_dict': data.cn_word_dict,
    'en_index_dict': data.en_index_dict,
    'cn_index_dict': data.cn_index_dict,
    'train_losses': train_losses,
    'epoch_losses': epoch_losses
}, config['save_file'])

print(f"<<<<<<< finished train, cost {time.time()-train_start:.4f} seconds")
print(f"Final model saved to: {config['save_file']}")

# 绘制训练损失曲线
plt.figure(figsize=(12, 5))

# 绘制每个batch的损失
plt.subplot(1, 2, 1)
plt.plot(train_losses)
plt.title('Training Loss per Batch')
plt.xlabel('Batch')
plt.ylabel('Loss')
plt.grid(True)

# 绘制每个epoch的平均损失
plt.subplot(1, 2, 2)
plt.plot(range(1, len(epoch_losses) + 1), epoch_losses, 'o-')
plt.title('Average Training Loss per Epoch')
plt.xlabel('Epoch')
plt.ylabel('Average Loss')
plt.grid(True)

plt.tight_layout()
plt.savefig(config['plot_file'], dpi=300, bbox_inches='tight')
plt.show()
print(f"Training loss curves saved to: {config['plot_file']}")




