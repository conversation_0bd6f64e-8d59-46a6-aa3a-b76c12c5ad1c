import jieba
import evaluate

# English Example
predictions = ["hello, I don't understand."]
references = [
    ["hello, I don't know."]
]
bleu = evaluate.load("bleu")
results = bleu.compute(predictions=predictions, references=references)
print(results)

# Chinese Example
space_sent = "因 此 ， 我 们 知 道 ， 洪 水 是 气 候 和 气 候 变 化 的 结 果 ， 不 同 的 发 生 在 不 同 的 情 况 下 发 生 了 不 同 的 。"
sent = ''
for word in space_sent:
    if word != " ":
        sent += word
print(sent)
words = list(jieba.cut(sent, cut_all=False)) # tokeize sentence using jieba
sent_pred = ''
for word in words:
    if sent_pred == '':
        sent_pred += word
    else:
        sent_pred += ' ' + word
print(sent_pred)

print()

sent = "洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。"
print(sent)
words = list(jieba.cut(sent, cut_all=False)) # tokeize sentence using jieba
sent_ref = ''
for word in words:
    if sent_ref == '':
        sent_ref += word
    else:
        sent_ref += ' ' + word
print(sent_ref)

print()

predictions = [sent_pred]
references = [
    [sent_ref]
]
bleu = evaluate.load("bleu")
results = bleu.compute(predictions=predictions, references=references)
print(results)

## reference code
# word segmentation on .txt file
# each line of this file includes one sentence
import jieba
import sys

input_file = sys.argv[1] # input text file
output_file = sys.argv[2] # output file to save results

with open(input_file,'r') as f2:
    sents = f2.readlines() # read lines of input file

lengths = []
with open(output_file,'w') as f1: # to save outputs
    for sent in sents: # one sentence at a time
        # f1.write(sent.strip()+'\t-->\t') # dump original sentence
        words = list(jieba.cut(sent, cut_all=False)) # tokeize sentence using jieba
        lengths.append(len(words)) # keep record of sentence lengths
        for word in words:
            if word == '\n':
                # f1.write(word.encode('utf-8'))
                f1.write(word)
            else:
            	# f1.write(word.encode('utf-8') + ' ') # dump tokenized sentence with tab separation
                f1.write(word + ' ')  
            # pdb.set_trace()


print('Minimum length: ',min(lengths))
print('Maximum length: ',max(lengths))
print('Average length: ',sum(lengths)/len(lengths))