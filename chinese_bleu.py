#!/usr/bin/env python
# coding: utf-8

"""
中文BLEU评估脚本
使用jieba进行中文分词，然后计算BLEU分数
"""

import jieba
import evaluate
import torch
import os
from model.transformer import build_transformer
from tokenization import PrepareData

def chinese_tokenize(text):
    """
    使用jieba对中文文本进行分词
    """
    # 移除特殊标记
    text = text.replace('BOS', '').replace('EOS', '').strip()
    # 使用jieba分词
    words = list(jieba.cut(text, cut_all=False))
    # 过滤空字符串和空格
    words = [w for w in words if w.strip()]
    return ' '.join(words)

def evaluate_model_bleu(model_path='save/models/model.pt'):
    """
    评估训练好的模型的BLEU分数
    """
    print("=" * 60)
    print("中文BLEU评估")
    print("=" * 60)

    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先运行 translator_en2cn.py 训练模型")
        return

    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 加载模型检查点
    print("加载模型...")
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']

    # 重建数据处理器
    data = PrepareData(config['train_file'], config['dev_file'], 1, 1, 0)

    # 重建模型
    model = build_transformer(
        checkpoint['src_vocab_size'], checkpoint['tgt_vocab_size'],
        config['seq_len'], config['seq_len'], config['d_model'],
        config['n_layer'], config['h_num'], config['dropout'], config['d_ff']
    ).to(device)

    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    print("开始BLEU评估...")

    # 示例评估（使用前几个验证样本）
    predictions = []
    references = []

    # 获取一些样本进行演示
    for i in range(min(5, len(data.dev_data))):
        # 获取源文本和目标文本
        source_text = " ".join([data.en_index_dict[w] for w in data.dev_en[i]])
        target_text = " ".join([data.cn_index_dict[w] for w in data.dev_cn[i]])

        # 这里我们使用目标文本作为"预测"来演示分词效果
        # 在实际应用中，这里应该是模型生成的翻译
        pred_text = target_text

        # 对中文进行分词
        pred_segmented = chinese_tokenize(pred_text)
        ref_segmented = chinese_tokenize(target_text)

        predictions.append(pred_segmented)
        references.append([ref_segmented])

        print(f"\n样本 {i+1}:")
        print(f"英文原文: {source_text}")
        print(f"中文原文: {target_text}")
        print(f"分词后: {ref_segmented}")

    # 计算BLEU分数
    if predictions and references:
        bleu = evaluate.load("bleu")
        try:
            results = bleu.compute(predictions=predictions, references=references)
            print(f"\nBLEU分数: {results['bleu']:.4f}")
            print(f"精确度分数: {results['precisions']}")
        except Exception as e:
            print(f"计算BLEU时出错: {e}")

    return predictions, references

# 演示中文分词和BLEU计算
print("中文分词和BLEU评估演示")
print("=" * 40)

# English Example
print("英文BLEU示例:")
predictions = ["hello, I don't understand."]
references = [["hello, I don't know."]]
bleu = evaluate.load("bleu")
results = bleu.compute(predictions=predictions, references=references)
print(f"英文BLEU结果: {results}")

print("\n" + "=" * 40)

# Chinese Example - 演示分词过程
print("中文分词演示:")
space_sent = "因 此 ， 我 们 知 道 ， 洪 水 是 气 候 和 气 候 变 化 的 结 果 ， 不 同 的 发 生 在 不 同 的 情 况 下 发 生 了 不 同 的 。"
sent = ''.join(space_sent.split())  # 移除空格
print(f"原始句子: {sent}")

# 使用jieba分词
words = list(jieba.cut(sent, cut_all=False))
sent_pred = ' '.join(words)
print(f"分词结果: {sent_pred}")

print()

# 参考句子
sent_ref = "洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。"
print(f"参考句子: {sent_ref}")
words_ref = list(jieba.cut(sent_ref, cut_all=False))
sent_ref_segmented = ' '.join(words_ref)
print(f"参考分词: {sent_ref_segmented}")

# 计算BLEU
predictions = [sent_pred]
references = [[sent_ref_segmented]]
results = bleu.compute(predictions=predictions, references=references)
print(f"\n中文BLEU结果: {results}")

print("\n" + "=" * 40)

# 如果模型已训练，尝试评估
print("尝试评估训练好的模型...")
try:
    evaluate_model_bleu()
except Exception as e:
    print(f"模型评估失败: {e}")
    print("请先运行 translator_en2cn.py 训练模型")

print("\n" + "=" * 60)
print("BLEU评估脚本演示完成")
print("=" * 60)

## reference code for batch processing
# word segmentation on .txt file
# each line of this file includes one sentence
def segment_file(input_file, output_file):
    """
    对文件中的每一行进行中文分词
    """
    import sys

    if len(sys.argv) < 3:
        print("用法: python chinese_bleu.py input_file output_file")
        return

    input_file = sys.argv[1] # input text file
    output_file = sys.argv[2] # output file to save results

    with open(input_file,'r', encoding='utf-8') as f2:
        sents = f2.readlines() # read lines of input file

    lengths = []
    with open(output_file,'w', encoding='utf-8') as f1: # to save outputs
        for sent in sents: # one sentence at a time
            words = list(jieba.cut(sent.strip(), cut_all=False)) # tokenize sentence using jieba
            lengths.append(len(words)) # keep record of sentence lengths
            for word in words:
                if word == '\n':
                    f1.write(word)
                else:
                    f1.write(word + ' ')
            f1.write('\n')

    print('Minimum length: ',min(lengths))
    print('Maximum length: ',max(lengths))
    print('Average length: ',sum(lengths)/len(lengths))

# 如果作为脚本运行且提供了命令行参数，则进行文件分词
if __name__ == "__main__":
    import sys
    if len(sys.argv) >= 3:
        segment_file(sys.argv[1], sys.argv[2])