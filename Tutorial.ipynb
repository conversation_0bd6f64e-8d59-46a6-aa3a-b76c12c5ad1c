{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Assignment: English to Chinese Translation Task\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this notebook, You will build a Transformer model (neural network model) to translate English sentence to Chinese sentences.\n", "\n", "The reference paper is [Attention Is All You Need](https://arxiv.org/pdf/1706.03762.pdf)\n", "\n", "![](document/images/google_translate.png )\n", "\n", "The full data set only contains around 10,000 sentence pairs. But this is very good practice to imporve your python programming skills and get deeper understanding of pytorch and Transformer(neural networks).\n", "\n", "![Attention Is All You Need](document/images/google_paper.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Understand the Transformer Model\n", " \n", "The Whole Transformer  encoder-decoder model architecture  services for the following purposes. \n", "\n", "\n", "- Encoder(s): the encoding process transforms the input sentence (list of English words) into numeric matrix format (embedding ), consider this step is to extract useful and necessary information for the decoder. In Fig 06, the embedding is represented by the green matrix.\n", "\n", "- Decoder(s): then the decoding process mapping these embeddings back to another language sequence as Fig 06 shown, which helps us to solve all kinds of supervised NLP tasks, like machine translation (in this blog), sentiment classification, entity recognition,  summary generation, semantic relation extraction and so on. \n", " \n", "![Understand_How_Transformer_Work](./document/images/Understand_How_Transformer_Work.png)\n", "\n", " "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Encoder \n", "  \n", "We will focus on the structure of the encoder in this section, because after understanding the structure of the encoder, understanding the decoder will be very simple. Moreover we can just use the encoder to complete some of the mainstream tasks in NLP, such as sentiment classification, semantic relationship analysis, named entity recognition and so on.\n", "\n", "Recall that the Encoder denotes the process of mapping natural language sequences to mathematical expressions to hidden layers outputs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Here is a Transformer Encoder Block structure**\n", "> Notification: the following sections will refer to the 1,2,3,4 blocks.\n", "\n", "![Transformer Encoder Stacks](./document/images/encoder.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.0 Data Preparation: English-to-Chinese Translator Data"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:00.985865Z", "start_time": "2020-09-20T00:27:00.980036Z"}}, "outputs": [], "source": ["import os\n", "import math\n", "import copy\n", "import time\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from nltk import word_tokenize\n", "from collections import Counter\n", "from torch.autograd import Variable\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt_tab to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Unzipping tokenizers\\punkt_tab.zip.\n", "[nltk_data] Downloading package punkt to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Unzipping tokenizers\\punkt.zip.\n", "[nltk_data] Downloading package wordnet to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data] Downloading package omw-1.4 to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# you may need to download following things for nltk package at the first access\n", "import nltk\n", "nltk.download('punkt_tab')\n", "nltk.download('punkt')\n", "nltk.download('wordnet')\n", "nltk.download('omw-1.4')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Input/Output Embeddings\n", "Similary to all sequential model, we used learned embedding to convert the input/output vectors' dimensionality to $d_{model}$.\n", "In our model, the two embedding layers and pre-softmax layer will share weight matrix."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:01.020560Z", "start_time": "2020-09-20T00:27:01.016575Z"}}, "outputs": [], "source": ["# Creating Input Embeddings\n", "class InputEmbeddings(nn.<PERSON><PERSON>le):\n", "    \n", "    def __init__(self, d_model: int, vocab_size: int):\n", "        super().__init__()\n", "        self.d_model = d_model # Dimension of vectors (512)\n", "        self.vocab_size = vocab_size # Size of the vocabulary\n", "        self.embedding = nn.Embedding(vocab_size, d_model) # PyTorch layer that converts integer indices to dense embeddings\n", "        \n", "    def forward(self, x):\n", "        return self.embedding(x) * math.sqrt(self.d_model) # Normalizing the variance of the embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we have all the code for data preprocessing. Let's focus on the understand and build Transformer mode. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Positional Encoding"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-09-19T02:20:41.866079Z", "start_time": "2020-09-19T02:20:41.858378Z"}}, "source": ["The $Transformer$ does **not** contain iteration operation like RNN or LSTM in encoders, so we have to offer the position information of the words to the model, so the model learns the order in the input sequence.  \n", "\n", "Thus, we define the **positional encoding** as [max_sequence_length, embedding_dimension]\n", "\n", "In the paper, we use sine and cosine function to provide the position information.\n", "\n", "$$PE_{(pos,2i)} = sin(pos / 10000^{2i/d_{\\text{model}}}) \\quad \\quad PE_{(pos,2i+1)} = cos(pos / 10000^{2i/d_{\\text{model}}})\\tag{eq.1}$$  "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:01.028057Z", "start_time": "2020-09-20T00:27:01.022506Z"}}, "outputs": [], "source": ["# Creating the Positional Encoding\n", "class PositionalEncoding(nn.Module):\n", "    \n", "    def __init__(self, d_model: int, seq_len: int, dropout: float) -> None:\n", "        super().__init__()\n", "        self.d_model = d_model # Dimensionality of the model\n", "        self.seq_len = seq_len # Maximum sequence length\n", "        self.dropout = nn.Dropout(dropout) # Dropout layer to prevent overfitting\n", "        \n", "        # Creating a positional encoding matrix of shape (seq_len, d_model) filled with zeros\n", "        pe = torch.zeros(seq_len, d_model) \n", "        \n", "        # Creating a tensor representing positions (0 to seq_len - 1)\n", "        position = torch.arange(0, seq_len, dtype = torch.float).unsqueeze(1) # Transforming 'position' into a 2D tensor['seq_len, 1']\n", "        \n", "        # Creating the division term for the positional encoding formula\n", "        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))\n", "        \n", "        # Apply sine to even indices in pe\n", "        pe[:, 0::2] = torch.sin(position * div_term)\n", "        # Apply cosine to odd indices in pe\n", "        pe[:, 1::2] = torch.cos(position * div_term)\n", "        \n", "        # Adding an extra dimension at the beginning of pe matrix for batch handling\n", "        pe = pe.unsqueeze(0)\n", "        \n", "        # Registering 'pe' as buffer. Buffer is a tensor not considered as a model parameter\n", "        self.register_buffer('pe', pe) \n", "        \n", "    def forward(self,x):\n", "        # Addind positional encoding to the input tensor X\n", "        x = x + (self.pe[:, :x.shape[1], :]).requires_grad_(False)\n", "        return self.dropout(x) # Dropout for regularization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["See, we first build the position encoding based on x and then add the 'pe' to the x in the forward function.\n", "\n", "> Notification: Set 'requires_grad=False'，because we do not need to train pe."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here are the position embedding visualisations, you can find the pattern changes with the increasing embedding dimensions. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pe = PositionalEncoding(32, 100, 0)  # d_model, dropout-ratio, max_len\n", "positional_encoding = pe.forward(torch.zeros(1, 100, 32))  # sequence length, d_model\n", "plt.figure(figsize=(10, 10))\n", "sns.heatmap(positional_encoding.squeeze())  # 100x32 matrix\n", "plt.title(\"Sinusoidal Function\")\n", "plt.xlabel(\"hidden dimension\")\n", "plt.ylabel(\"sequence length\")\n", "\n", "\n", "plt.figure(figsize=(15, 5))\n", "pe = PositionalEncoding(24, 100, 0)\n", "y = pe.forward(torch.zeros(1, 100, 24))\n", "plt.plot(np.arange(100), y[0, :, 5:10].data.numpy())\n", "plt.legend([\"dim %d\" % p for p in [5, 6, 7, 8, 9]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Self Attention and Mask"]}, {"cell_type": "markdown", "metadata": {}, "source": ["An attention function can be described as mapping a query and a set of key-value pairs to an output, where the query, keys, values, and output are all vectors. The output is computed as a weighted sum of the values, where the weight assigned to each value is computed by a compatibility function of the query with the corresponding key.\n", "\n", "We call our particular attention “Scaled Dot-Product Attention”. The input consists of queries and keys of dimension $d_k$, and values of dimension $d_v$. \n", "\n", "We compute the dot products of the query with all keys, divide each by $\\sqrt{d_k}$, and apply a softmax function to obtain the weights on the values.\n", "\n", "![self_attention](document/images/self-attention.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": [" The two most commonly used attention functions are additive attention, and dot-product (multiplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor of $\\frac{1}{\\sqrt{d_k}}$\n", ". Additive attention computes the compatibility function using a feed-forward network with a single hidden layer. While the two are similar in theoretical complexity, dot-product attention is much faster and more space-efficient in practice, since it can be implemented using highly optimized matrix multiplication code.\n", "\n", "To illustrate why the dot products get large, assume that the components of q and k are independent random variables with mean 0  and variance 1 . Then their dot product, $q\\cdot k $ has mean 0 and variance $d_k$,To counteract this effect, we scale the dot products by  $\\frac{1}{\\sqrt{d_k}}$."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Multi-head attention allows the model to jointly attend to information from different representation subspaces at different positions. With a single attention head, averaging inhibits this."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Attention Mask**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The input $X$ is $[batch-size,  sequence-length]$, we use 'padding' to fill the matrix with 0 with respect to the longest sequence. \n", "\n", "But this will case issues for the softmax computation. \n", "$\\sigma(\\mathbf {z})_{i}={\\frac {e^{z_{i}}}{\\sum _{j=1}^{K}e^{z_{j}}}}$, where $e^0=1$.\n", "\n", "This means the padding sections join the computation, but they shouldn't. So we create this mask to ignore these area by assign a large negative bias.\n", " \n", "$$z_{illegal} = z_{illegal} + bias_{illegal}$$\n", "$$bias_{illegal} \\to -\\infty$$\n", "$$e^{z_{illegal}} \\to 0 $$  \n", "  \n", "Thus, the masked area will lead to 0 so we avoid them in computation.\n", "\n", "> Notification: in self-attention compution, we use mini-batch data as input, means we feed multiply lines of sentences into the model for training and computation. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](document/images/attention_mask.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In Transformer, both encoder and decoder attention computations need masking operation, but their functions are different.\n", "\n", "In the decoder, the self-attention layer is only allowed to attend to earlier positions in the output sequence. This is done by masking future positions (setting them to '-inf') before the softmax step in the self-attention calculation.\n", "\n", "The “Encoder-Decoder Attention” layer works just like multiheaded self-attention, except it creates its Queries matrix from the layer below it, and takes the Keys and Values matrix from the output of the encoder stack.\n", "  \n", "Here, we define a batch object that holds the src (English) and target sentences (Chinese) for training, as well as constructing the masks.\n", "\n", "> Notification: Mask(Opt.) is between Scale and Softmax\n", " "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.129099Z", "start_time": "2020-09-20T00:27:02.124593Z"}}, "outputs": [], "source": ["class MaskBatch:\n", "    '''Object for holding a batch of data with mask during training.'''\n", "    def __init__(self, src, tgt=None, pad_id=0):\n", "        # convert words id to long format.\n", "        src = torch.from_numpy(src).long()\n", "        tgt = torch.from_numpy(tgt).long()\n", "        self.src = src\n", "        # get the padding postion binary mask \n", "        self.src_mask = (src != pad_id).unsqueeze(1).unsqueeze(1) # mask [B 1 1 src_L]\n", "        if tgt is not None:\n", "            # decoder input\n", "            self.tgt = tgt[:, :-1]\n", "            # decoder target\n", "            self.tgt_y = tgt[:, 1:]\n", "            # add attention mask to decoder input\n", "            self.tgt_mask = self.make_decoder_mask(self.tgt, pad_id)\n", "            # check decoder output padding number\n", "            self.ntokens = (self.tgt_y != pad_id).data.sum()\n", "\n", "    def make_decoder_mask(self, tgt, pad_id):\n", "        \"Create a mask to hide padding and future words.\"\n", "        tgt_mask = (tgt != pad_id).unsqueeze(1).unsqueeze(1) # mask [B 1 1 tgt_L] \n", "        tgt_mask = tgt_mask & casual_mask(tgt.size(-1)).unsqueeze(1).type_as(tgt_mask.data) \n", "        return tgt_mask "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Layer Normalization and Residual Connection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1). **LayerNorm**:   \n", "  \n", "**Layer Normalization** normalize the hidden layer output into standard format, i.i.d, to boost the training efficency and model weight convergence (row wise). \n", "$$\\mu_{i}=\\frac{1}{m} \\sum^{m}_{j=1}x_{ij}$$  \n", "  \n", "$$\\sigma^{2}_{i}=\\frac{1}{m} \\sum^{m}_{j=1}\n", "(x_{ij}-\\mu_{i})^{2}$$  \n", "  \n", "$$LayerNorm(x)=\\alpha \\odot \\frac{x_{ij}-\\mu_{i}}\n", "{\\sqrt{\\sigma^{2}_{i}+\\epsilon}} + \\beta \\tag{eq.5}$$  \n", "  \n", "$\\epsilon$ is to avoid $0$ division; **$\\alpha, \\beta$** are parameter, $\\odot$ denotes element-wise product. Normally, we initialize $\\alpha$ as 1s and $\\beta$ as 0s."]}, {"cell_type": "markdown", "metadata": {}, "source": ["2). **Residual Connection**:   \n", " \n", "We employ a residual connection  around each of the two sub-layers, followed by layer normalization.\n", "We get the Value matrix with the weights from attenations $Attention(Q,  K, V)$, and then we \n", "transpose it to make sure it shares the same shape of $X_{embedding}:[batch.size, sequence.length, embedding.dimension]$. And then add them together.\n", "\n", "$$X_{embedding} + Attention(Q, K, V)$$  \n", "  \n", "In the following compuations, after each module, we add the input with the output of the module to get residual connection, which allows the gradients be back-propogated to the start layers. \n", "$$X + SubLayer(X) \\tag{eq. 6}$$  \n", "  \n", "> **Notification**: to $SubLayer(X)$ we call the dropout function and then add x, $X + Dropout(SubLayer(X))$"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.134759Z", "start_time": "2020-09-20T00:27:02.130657Z"}}, "outputs": [], "source": ["# Creating Layer Normalization\n", "class LayerNormalization(nn.Module):\n", "    \n", "    def __init__(self, eps: float = 10**-6) -> None: # We define epsilon as 0.000001 to avoid division by zero\n", "        super().__init__()\n", "        self.eps = eps\n", "        \n", "        # We define alpha as a trainable parameter and initialize it with ones\n", "        self.alpha = nn.Parameter(torch.ones(1)) # One-dimensional tensor that will be used to scale the input data\n", "        \n", "        # We define bias as a trainable parameter and initialize it with zeros\n", "        self.bias = nn.Parameter(torch.zeros(1)) # One-dimensional tenso that will be added to the input data\n", "        \n", "    def forward(self, x):\n", "        mean = x.mean(dim = -1, keepdim = True) # Computing the mean of the input data. Keeping the number of dimensions unchanged\n", "        std = x.std(dim = -1, keepdim = True) # Computing the standard deviation of the input data. Keeping the number of dimensions unchanged\n", "        \n", "        # Returning the normalized input\n", "        return self.alpha * (x-mean) / (std + self.eps) + self.bias"]}, {"cell_type": "markdown", "metadata": {}, "source": ["PyTor<PERSON> has nn.<PERSON>erNorm，but we apply math equations here to learn. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.139180Z", "start_time": "2020-09-20T00:27:02.136279Z"}}, "outputs": [], "source": ["# Building Residual Connection\n", "class ResidualConnection(nn.Module):\n", "    def __init__(self, dropout: float) -> None:\n", "        super().__init__()\n", "        self.dropout = nn.Dropout(dropout) # We use a dropout layer to prevent overfitting\n", "        self.norm = LayerNormalization() # We use a normalization layer \n", "    \n", "    def forward(self, x, sublayer):\n", "        # We normalize the input and add it to the original input 'x'. This creates the residual connection process.\n", "        return x + self.dropout(sublayer(self.norm(x))) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 Feedforwad Networks \n", "**Position-wise Feed-Forward Networks**\n", "\n", "In addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully connected feed-forward network, which is applied to each position separately and identically. This consists of two linear transformations with a ReLU activation in between.\n", "\n", "$$ FFN(x) = max(0, xW_1 + b_1)W_2 + b_2$$\n", "\n", "While the linear transformations are the same across different positions, they use different parameters from layer to layer. Another way of describing this is as two convolutions with kernel size 1.\n", "\n", "The dimensionality of input and output is $d_{model}$, and the inner-layer has dimensionality $d_{ff}$."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.5 Transformer Encoder Overview"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we have programmed the four parts of the Transformer Encoder. Let us review how the data are transformed through all these layers.\n", "\n", "1. **Word Embedding and Positional Encoding**:\n", "\n", "$$X = EmbeddingLookup(X) + PositionalEncoding(X)$$\n", "\n", "$$X \\in \\mathbb{R}^{batch.size \\times  seq.len.\\times   embed.dim.} $$  \n", "  \n", "2. **Self-Attention and Mask**:\n", "\n", "$$Q = Linear(X) = XW_{Q}$$ \n", "$$K = Linear(X) = XW_{K}$$\n", "$$V = Linear(X) = XW_{V}$$\n", "$$X_{attention} = SelfAttention(Q, K, V)$$  \n", "\n", "3. **Residual Connection and Layer Normalization**:\n", "\n", "$$X_{attention} = LayerNorm(X_{attention})$$\n", "$$X_{attention} = X + X_{attention} $$  \n", "\n", "4. **Position-wise Feed-Forward Networks** two linear mappings with ReLU Avtivation function:\n", "$$X_{hidden} = Linear(Activate(Linear(X_{attention})))$$  \n", "  \n", "5. **Repeat 3** :\n", "$$X_{hidden} = LayerNorm(X_{hidden})$$\n", "$$X_{hidden} = X_{attention} + X_{hidden}$$\n", "\n", "$$X_{hidden} \\in \\mathbb{R}^{batch.size \\times  seq.len.\\times   embed.dim.}$$  \n", "  "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.147675Z", "start_time": "2020-09-20T00:27:02.145260Z"}}, "outputs": [], "source": ["def clones(module, N):\n", "    \"\"\"\n", "    \"Produce N identical layers.\"\n", "    Use deepcopy the weight are indenpendent.\n", "    \"\"\"\n", "    return nn.ModuleList([copy.deepcopy(module) for _ in range(N)])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the paper, $Encoder$ has $N=6$ blocks."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.152154Z", "start_time": "2020-09-20T00:27:02.149114Z"}}, "outputs": [], "source": ["# An Encoder can have several Encoder Blocks\n", "class Encoder(nn.Module):\n", "    \n", "    # The Encoder takes in instances of 'EncoderBlock'\n", "    def __init__(self, layers: nn.ModuleList) -> None:\n", "        super().__init__()\n", "        self.layers = layers # Storing the EncoderBlocks\n", "        self.norm = LayerNormalization() # Layer for the normalization of the output of the encoder layers\n", "        \n", "    def forward(self, x, mask):\n", "        # Iterating over each EncoderBlock stored in self.layers\n", "        for layer in self.layers:\n", "            x = layer(x, mask) # Applying each EncoderBlock to the input tensor 'x'\n", "        return self.norm(x) # Normalizing output\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Each **Encoder Block** contains two sub-layers(**Self-Attention**,**Position-wise**) and 2 sublayer-connetions:\n", " "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.157528Z", "start_time": "2020-09-20T00:27:02.153637Z"}}, "outputs": [], "source": ["# Building Encoder Block\n", "class EncoderBlock(nn.Module):\n", "    \n", "    # This block takes in the MultiHeadAttentionBlock and FeedForwardBlock, as well as the dropout rate for the residual connections\n", "    def __init__(self, self_attention_block: MultiHeadAttentionBlock, feed_forward_block: FeedForwardBlock, dropout: float) -> None:\n", "        super().__init__()\n", "        # Storing the self-attention block and feed-forward block\n", "        self.self_attention_block = self_attention_block\n", "        self.feed_forward_block = feed_forward_block\n", "        self.residual_connections = nn.ModuleList([ResidualConnection(dropout) for _ in range(2)]) # 2 Residual Connections with dropout\n", "        \n", "    def forward(self, x, src_mask):\n", "        # Applying the first residual connection with the self-attention block\n", "        x = self.residual_connections[0](x, lambda x: self.self_attention_block(x, x, x, src_mask)) # Three 'x's corresponding to query, key, and value inputs plus source mask\n", "        \n", "        # Applying the second residual connection with the feed-forward block \n", "        x = self.residual_connections[1](x, self.feed_forward_block)\n", "        return x # Output tensor after applying self-attention and feed-forward layers with residual connections.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.  <PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-09-19T11:07:22.025892Z", "start_time": "2020-09-19T11:07:22.008459Z"}}, "source": ["After the introduction of the encoder structure, we can see the decoder shares a lot similarities of encoder.\n", "It also stacks N times. But there is a Encoder-Deconder-Contex-Attention layer (sublayer[1]) between the Masked MHA[0] and FFN[2]. It use the output of the decoder as query to search the output of encoder with MHA, which makes decoder see all the outputs from encoder.\n", "\n", "Decoding process:\n", "- Input: Encoding output(memory) and i-1 position decoder output/\n", "- Output: i position output work probabilities.\n", "- decoding process works like RNN."]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Decoder Structure](document/images/decoder.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.169437Z", "start_time": "2020-09-20T00:27:02.159137Z"}}, "outputs": [], "source": ["# Building Decoder Block\n", "class DecoderBlock(nn.Module):\n", "    \n", "    # The DecoderBlock takes in two MultiHeadAttentionBlock. One is self-attention, while the other is cross-attention.\n", "    # It also takes in the feed-forward block and the dropout rate\n", "    def __init__(self,  self_attention_block: MultiHeadAttentionBlock, cross_attention_block: MultiHeadAttentionBlock, feed_forward_block: FeedForwardBlock, dropout: float) -> None:\n", "        super().__init__()\n", "        self.self_attention_block = self_attention_block\n", "        self.cross_attention_block = cross_attention_block\n", "        self.feed_forward_block = feed_forward_block\n", "        self.residual_connections = nn.ModuleList([ResidualConnection(dropout) for _ in range(3)]) # List of three Residual Connections with dropout rate\n", "        \n", "    def forward(self, x, encoder_output, src_mask, tgt_mask):\n", "        \n", "        # Self-Attention block with query, key, and value plus the target language mask\n", "        x = self.residual_connections[0](x, lambda x: self.self_attention_block(x, x, x, tgt_mask))\n", "        \n", "        # The Cross-Attention block using two 'encoder_ouput's for key and value plus the source language mask. It also takes in 'x' for Decoder queries\n", "        x = self.residual_connections[1](x, lambda x: self.cross_attention_block(x, encoder_output, encoder_output, src_mask))\n", "        \n", "        # Feed-forward block with residual connections\n", "        x = self.residual_connections[2](x, self.feed_forward_block)\n", "        return x\n", "\n", "\n", "\n", "# Building Decoder\n", "# A Decoder can have several Decoder Blocks\n", "class Decoder(nn.Module):\n", "    \n", "    # The Decoder takes in instances of 'DecoderBlock'\n", "    def __init__(self, layers: nn.ModuleList) -> None:\n", "        super().__init__()\n", "        \n", "        # Storing the 'DecoderBlock's\n", "        self.layers = layers\n", "        self.norm = LayerNormalization() # Layer to normalize the output\n", "        \n", "    def forward(self, x, encoder_output, src_mask, tgt_mask):\n", "        \n", "        # Iterating over each DecoderBlock stored in self.layers\n", "        for layer in self.layers:\n", "            # Applies each DecoderBlock to the input 'x' plus the encoder output and source and target masks\n", "            x = layer(x, encoder_output, src_mask, tgt_mask)\n", "        return self.norm(x) # Returns normalized output\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We also modify the self-attention sub-layer in the decoder stack to prevent positions from attending to subsequent positions. This masking (**casual_mask**), combined with fact that the output embeddings are offset by one position, ensures that the predictions for position i can depend only on the known outputs at positions less than i .\n", "\n", "\n", "For Encoder src-mask, just mask the padding cells. \n", "But for decoder trg-mask, we need mask the padding and add the subsequent-mask process.  "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.174560Z", "start_time": "2020-09-20T00:27:02.171267Z"}}, "outputs": [], "source": ["def casual_mask(size):\n", "    # Creating a square matrix of dimensions 'size x size' filled with ones\n", "    mask = torch.triu(torch.ones(1, size, size), diagonal=1).type(torch.int)\n", "    return mask == 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Below the attention mask shows the position each tgt word (row) is allowed to look at (column). Words are blocked for attending to future words during training.\n", "\"white\" color denote True."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.323043Z", "start_time": "2020-09-20T00:27:02.176195Z"}}, "outputs": [], "source": ["subsequent_mask = casual_mask(8)\n", "\n", "plt.figure(figsize=(5, 5))\n", "plt.imshow(subsequent_mask[0], cmap='gray')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Transformer Model\n", "  \n", "Finally, let us put encoder and decoder together with the 'generator'.\n", "\n", "![](document/images/English-to-Chinese.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Recall the decoder then generates an output sequence, of symbols one element at a time. At each step the model is auto-regressive (cite), consuming the previously generated symbols as additional input when generating the next. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.332072Z", "start_time": "2020-09-20T00:27:02.325677Z"}}, "outputs": [], "source": ["class Transformer(nn.Mo<PERSON>le):\n", "    \n", "    # This takes in the encoder and decoder, as well the embeddings for the source and target language.\n", "    # It also takes in the Positional Encoding for the source and target language, as well as the projection layer\n", "    def __init__(self, encoder: Encoder, decoder: Decoder, src_embed: InputEmbeddings, tgt_embed: InputEmbeddings, src_pos: PositionalEncoding, tgt_pos: PositionalEncoding, projection_layer: ProjectionLayer) -> None:\n", "        super().__init__()\n", "        self.encoder = encoder\n", "        self.decoder = decoder\n", "        self.src_embed = src_embed\n", "        self.tgt_embed = tgt_embed\n", "        self.src_pos = src_pos\n", "        self.tgt_pos = tgt_pos\n", "        self.projection_layer = projection_layer\n", "        \n", "    # Encoder     \n", "    def encode(self, src, src_mask):\n", "        src = self.src_embed(src) # Applying source embeddings to the input source language\n", "        src = self.src_pos(src) # Applying source positional encoding to the source embeddings\n", "        return self.encoder(src, src_mask) # Returning the source embeddings plus a source mask to prevent attention to certain elements\n", "    \n", "    # Decoder\n", "    def decode(self, encoder_output, src_mask, tgt, tgt_mask):\n", "        tgt = self.tgt_embed(tgt) # Applying target embeddings to the input target language (tgt)\n", "        tgt = self.tgt_pos(tgt) # Applying target positional encoding to the target embeddings\n", "        \n", "        # Returning the target embeddings, the output of the encoder, and both source and target masks\n", "        # The target mask ensures that the model won't 'see' future elements of the sequence\n", "        return self.decoder(tgt, encoder_output, src_mask, tgt_mask)\n", "    \n", "    # Applying Projection Layer with the Softmax function to the Decoder output\n", "    def project(self, x):\n", "        return self.projection_layer(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.341832Z", "start_time": "2020-09-20T00:27:02.338069Z"}}, "outputs": [], "source": ["# Buiding Linear Layer\n", "class ProjectionLayer(nn.Module):\n", "    def __init__(self, d_model: int, vocab_size: int) -> None: # Model dimension and the size of the output vocabulary\n", "        super().__init__()\n", "        self.proj = nn.Linear(d_model, vocab_size) # Linear layer for projecting the feature space of 'd_model' to the output space of 'vocab_size'\n", "    def forward(self, x):\n", "        return torch.log_softmax(self.proj(x), dim = -1) # Applying the log Softmax function to the output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Set Parameters and Create the Full Transformer model Function**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.355463Z", "start_time": "2020-09-20T00:27:02.349624Z"}}, "outputs": [], "source": ["# Definin function and its parameter, including model dimension, number of encoder and decoder stacks, heads, etc.\n", "def build_transformer(src_vocab_size: int, tgt_vocab_size: int, src_seq_len: int, tgt_seq_len: int, d_model: int = 512, N: int = 6, h: int = 8, dropout: float = 0.1, d_ff: int = 2048) -> Transformer:\n", "    \n", "    # Creating Embedding layers\n", "    src_embed = InputEmbeddings(d_model, src_vocab_size) # Source language (Source Vocabulary to 512-dimensional vectors)\n", "    tgt_embed = InputEmbeddings(d_model, tgt_vocab_size) # Target language (Target Vocabulary to 512-dimensional vectors)\n", "    \n", "    # Creating Positional Encoding layers\n", "    src_pos = PositionalEncoding(d_model, src_seq_len, dropout) # Positional encoding for the source language embeddings\n", "    tgt_pos = PositionalEncoding(d_model, tgt_seq_len, dropout) # Positional encoding for the target language embeddings\n", "    \n", "    # Creating EncoderBlocks\n", "    encoder_blocks = [] # Initial list of empty EncoderBlocks\n", "    for _ in range(N): # Iterating 'N' times to create 'N' EncoderBlocks (N = 6)\n", "        encoder_self_attention_block = MultiHeadAttentionBlock(d_model, h, dropout) # Self-Attention\n", "        feed_forward_block = FeedForwardBlock(d_model, d_ff, dropout) # FeedForward\n", "        \n", "        # Combine layers into an EncoderBlock\n", "        encoder_block = EncoderBlock(encoder_self_attention_block, feed_forward_block, dropout)\n", "        encoder_blocks.append(encoder_block) # Appending EncoderBlock to the list of EncoderBlocks\n", "        \n", "    # Creating DecoderBlocks\n", "    decoder_blocks = [] # Initial list of empty DecoderBlocks\n", "    for _ in range(N): # Iterating 'N' times to create 'N' DecoderBlocks (N = 6)\n", "        decoder_self_attention_block = MultiHeadAttentionBlock(d_model, h, dropout) # Self-Attention\n", "        decoder_cross_attention_block = MultiHeadAttentionBlock(d_model, h, dropout) # Cross-Attention\n", "        feed_forward_block = FeedForwardBlock(d_model, d_ff, dropout) # FeedForward\n", "        \n", "        # Combining layers into a DecoderBlock\n", "        decoder_block = DecoderBlock(decoder_self_attention_block, decoder_cross_attention_block, feed_forward_block, dropout)\n", "        decoder_blocks.append(decoder_block) # Appending DecoderBlock to the list of DecoderBlocks\n", "        \n", "    # Creating the Encoder and Decoder by using the EncoderBlocks and DecoderBlocks lists\n", "    encoder = Encoder(nn.ModuleList(encoder_blocks))\n", "    decoder = Decoder(nn.ModuleList(decoder_blocks))\n", "    \n", "    # Creating projection layer\n", "    projection_layer = ProjectionLayer(d_model, tgt_vocab_size) # Map the output of Decoder to the Target Vocabulary Space\n", "    \n", "    # Creating the transformer by combining everything above\n", "    transformer = Transformer(encoder, decoder, src_embed, tgt_embed, src_pos, tgt_pos, projection_layer)\n", "    \n", "    # Initialize the parameters\n", "    for p in transformer.parameters():\n", "        if p.dim() > 1:\n", "            nn.init.xavier_uniform_(p)\n", "            \n", "    return transformer # Assembled and initialized Transformer. Ready to be trained and validated!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Transformer Model Training: English-to-Chinese  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Regularization **Label Smoothing**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["During training, you can employed label smoothing of value $\\epsilon_{ls}=0.1$ (https://arxiv.org/pdf/1512.00567.pdf). \n", "\n", "This hurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Loss Computation**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-20T00:27:02.611144Z", "start_time": "2020-09-20T00:27:02.606658Z"}}, "outputs": [], "source": ["loss_fn = nn.CrossEntropyLoss(ignore_index=PAD, label_smoothing=0.).to(device)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Optimizer with Warmup Learning Rate**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["According to the paper, they applied a warmup learning rate with Adam Optimizer with $\\beta_1=0.9、\\beta_2=0.98$ 和 $\\epsilon = 10^{−9}$.  \n", "\n", "This will update the learning rate over the course of training, according to the formula:\n", "\n", "$$lrate = d^{−0.5}_{model}⋅min(step\\_num^{−0.5},\\; step\\_num⋅warmup\\_steps^{−1.5})$$  \n", "\n", "This corresponds to increasing the learning rate linearly for the first \"warmup_steps\" training steps, and decreasing it thereafter proportionally to the inverse square root of the step number."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This need you own to implement\n", "# you can use scheduler in pytorch to adjust the learning rate."]}, {"cell_type": "markdown", "metadata": {}, "source": ["all the updates are for the learning rate, \n", "- model-size denotes $d_{model}$. \n", "- warmup denotes  warmup-steps.\n", "- factor is a scalar."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Example of the curves of this model for different model sizes and for optimization hyperparameters."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Training Iterators**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Training model\n", "print(\">>>>>>> start train\")\n", "train_start = time.time()\n", "\n", "# Initializing epoch and global step variables\n", "initial_epoch = 0\n", "global_step = 0\n", "\n", "# Iterating over each epoch from the 'initial_epoch' variable up to the number of epochs informed in the config\n", "for epoch in range(initial_epoch, config['num_epochs']):\n", "    # Initializing an iterator over the training dataloader\n", "    # We also use tqdm to display a progress bar\n", "    batch_iterator = tqdm(data.train_data, desc = f'Processing epoch {epoch:02d}')\n", "    \n", "    # For each batch...\n", "    for batch in batch_iterator:\n", "        model.train() # Train the model\n", "        \n", "        # Loading input data and masks onto the GPU\n", "        encoder_input = batch.src.to(device)\n", "        decoder_input = batch.tgt.to(device)\n", "        encoder_mask = batch.src_mask.to(device)\n", "        decoder_mask = batch.tgt_mask.to(device)\n", "        # print(encoder_input[0], encoder_mask[0], decoder_input[0], decoder_mask[0])\n", "        # print(encoder_input.shape, encoder_mask.shape, decoder_input.shape, decoder_mask.shape)\n", "\n", "        # Running tensors through the Transformer\n", "        encoder_output = model.encode(encoder_input, encoder_mask)\n", "        decoder_output = model.decode(encoder_output, encoder_mask, decoder_input, decoder_mask)\n", "        proj_output = model.project(decoder_output)\n", "        \n", "        # Loading the target labels onto the GPU\n", "        label = batch.tgt_y.to(device)\n", "        \n", "        # Computing loss between model's output and true labels\n", "        loss = loss_fn(proj_output.view(-1, tgt_vocab_size), label.view(-1))\n", "        \n", "        # Updating progress bar\n", "        batch_iterator.set_postfix({f\"loss\": f\"{loss.item():6.3f}\"})\n", "        \n", "        # Performing backpropagation\n", "        loss.backward()\n", "        \n", "        # Updating parameters based on the gradients\n", "        optimizer.step()\n", "        \n", "        # Clearing the gradients to prepare for the next batch\n", "        optimizer.zero_grad()\n", "        \n", "        global_step += 1 # Updating global step count\n", "\n", "    # to evaluate model performance\n", "    if epoch % 5 == 0:\n", "        run_validation(model, data, data.cn_word_dict, config['seq_len'], device, lambda msg: batch_iterator.write(msg))\n", "\n", "print(f\"<<<<<<< finished train, cost {time.time()-train_start:.4f} seconds\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Prediction with English-to-Chinese Translator"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-19T13:33:30.299613Z", "start_time": "2020-09-19T13:33:30.295197Z"}}, "outputs": [], "source": ["# Define function to obtain the most probable next token\n", "def greedy_decode(model, source, source_mask, tokenizer_tgt, max_len, device):\n", "    # Retrieving the indices from the start and end of sequences of the target tokens\n", "    bos_id = tokenizer_tgt.get('BOS')\n", "    eos_id = tokenizer_tgt.get('EOS')\n", "    \n", "    # Computing the output of the encoder for the source sequence\n", "    encoder_output = model.encode(source, source_mask)\n", "    # Initializing the decoder input with the Start of Sentence token\n", "    decoder_input = torch.empty(1,1).fill_(bos_id).type_as(source).to(device)\n", "    \n", "    # Looping until the 'max_len', maximum length, is reached\n", "    while True:\n", "        if decoder_input.size(1) == max_len:\n", "            break\n", "            \n", "        # Building a mask for the decoder input\n", "        decoder_mask = casual_mask(decoder_input.size(1)).type_as(source_mask).to(device)\n", "        \n", "        # Calculating the output of the decoder\n", "        out = model.decode(encoder_output, source_mask, decoder_input, decoder_mask)\n", "        \n", "        # Applying the projection layer to get the probabilities for the next token\n", "        prob = model.project(out[:, -1])\n", "        \n", "        # Selecting token with the highest probability\n", "        _, next_word = torch.max(prob, dim=1)\n", "        decoder_input = torch.cat([decoder_input, torch.empty(1,1). type_as(source).fill_(next_word.item()).to(device)], dim=1)\n", "        \n", "        # If the next token is an End of Sentence token, we finish the loop\n", "        if next_word == eos_id:\n", "            break\n", "            \n", "    return decoder_input.squeeze(0) # Sequence of tokens generated by the decoder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["English to Chinese Translations"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-19T13:33:30.306447Z", "start_time": "2020-09-19T13:33:30.301271Z"}}, "outputs": [], "source": ["def run_validation(model, data, tokenizer_tgt, max_len, device, print_msg, num_examples=4):\n", "    model.eval() # Setting model to evaluation mode\n", "    count = 0 # Initializing counter to keep track of how many examples have been processed\n", "    \n", "    console_width = 80 # Fixed witdh for printed messages\n", "    \n", "    # Creating evaluation loop\n", "    with torch.no_grad(): # Ensuring that no gradients are computed during this process\n", "        for i, batch in enumerate(data.dev_data):\n", "            count += 1\n", "            encoder_input = batch.src.to(device)\n", "            encoder_mask = batch.src_mask.to(device)\n", "            \n", "            # Ensuring that the batch_size of the validation set is 1\n", "            assert encoder_input.size(0) ==  1, 'Batch size must be 1 for validation.'\n", "            \n", "            # Applying the 'greedy_decode' function to get the model's output for the source text of the input batch\n", "            model_out = greedy_decode(model, encoder_input, encoder_mask, tokenizer_tgt, max_len, device)\n", "\n", "            # Retrieving source and target texts from the batch\n", "            source_text = \" \".join([data.en_index_dict[w] for w in data.dev_en[i]])\n", "            target_text = \" \".join([data.cn_index_dict[w] for w in data.dev_cn[i]])\n", "\n", "            # save all in the translation list\n", "            model_out_text = []\n", "            # convert id to Chinese, skip 'BOS' 0.\n", "            print(model_out)\n", "            for j in range(1, model_out.size(0)):\n", "                sym = data.cn_index_dict[model_out[j].item()]\n", "                if sym != 'EOS':\n", "                    model_out_text.append(sym)\n", "                else:\n", "                    break\n", "\n", "            # Printing results\n", "            print_msg('-'*console_width)\n", "            print_msg(f'SOURCE: {source_text}')\n", "            print_msg(f'TARGET: {target_text}')\n", "            print_msg(f'PREDICTED: {model_out_text}')\n", "            \n", "            # After two examples, we break the loop\n", "            if count == num_examples:\n", "                break\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**English to Chinese Translator** "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-09-19T13:33:32.915034Z", "start_time": "2020-09-19T13:33:30.308167Z"}}, "outputs": [], "source": ["# Implement the BLEU score function to test your model"]}], "metadata": {"kernelspec": {"display_name": "translation", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}, "nteract": {"version": "0.25.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": true, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "256px"}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "position": {"height": "557px", "left": "447px", "right": "20px", "top": "255px", "width": "683px"}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}